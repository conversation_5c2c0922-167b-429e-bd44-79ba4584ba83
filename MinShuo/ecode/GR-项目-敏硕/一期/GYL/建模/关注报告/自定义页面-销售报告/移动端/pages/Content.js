const {inject, observer} = mobxReact;
const {toJS} = mobx;
const {Form, SearchAdvanced, <PERSON><PERSON><PERSON>, BrowserHrm} = WeaverMobilePage;
const {Result, Icon, Button, SearchBar, DatePicker, List, Toast} = WeaverMobile;
const {withRouter} = ReactRouterDom;
const {DatePickerInput} = DatePicker;

//读取config
const config = ecodeSDK.getCom("${appId}", "config");


@inject('basicStore')
@withRouter
@observer
class Content extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            option: {},
            hasData: true, // 默认有数据
            data: [], // 数据列表
            page: 1,
            pageSize: 10,
            loading: false, //接口读取数据的loading
            finished: false,
            allData: [],
            canLoadMore: false, // 新增：只有下拉到提示后再继续下拉才加载
            normal_display: "flex"
        }
        this.scrollRef = React.createRef();
        this.listEndRef = React.createRef();
    }

    componentWillMount() {

    }

    componentDidMount() {
        //传递当前实例，给到父类
        this.props.setPageRef(this)
        const container = document.getElementById('SD_Page');
        if (container) {
            console.log("componentDidMount container", container)
            container.addEventListener('scroll', this.handleScroll);

        }
        // 页面初始时主动判断一次
        setTimeout(() => {
            this.handleScroll();
        }, 0);
    }

    componentWillUnmount() {
        const container = document.getElementById('SD_Page');
        if (container) {
            container.removeEventListener('scroll', this.handleScroll);
        }
    }

    /**
     * 刷新
     * @param khmc
     * @param reporter
     * @param startdate
     * @param enddate
     */
    refresh = (khmc, reporter, startdate, enddate) => {
        let that = this;
        this.setState({
            khmc: khmc,
            reporter: reporter,
            startdate: startdate,
            enddate: enddate,
        }, () => {
            that.doRefresh()
        })
    }

    /**
     * 刷新
     */
    doRefresh = () => {
        let that = this;
        const {khmc, reporter, startdate, enddate, pageSize} = this.state;
        //使用接口获取数据
        const {http, util} = window.GRSDK
        let params = {
            port: config.port,//当前服务器内网环境端口号（后端调用建模查询接口时使用）
            auth_customid: config.auth_customid,//权限建模查询id
            khmc_paramname: config.khmc_paramname,//权限查询条件字段id-客户
            bgr_paramname: config.bgr_paramname,//权限查询条件字段id-报告人
            bgrq_paramname: config.bgrq_paramname,//权限查询条件字段id-报告日期
            report_modid: config.report_modid,//报告建模id
            fieldid_bffs: config.fieldid_bffs,//主表字段id-拜访方式（查询下拉框值需要用到）
            fieldid_jd: config.fieldid_jd, //明细2字段id-阶段（查询下拉框值需要用到）
            fieldid_gjjh: config.fieldid_gjjh,//主表字段id-跟进计划（查询下拉框值需要用到）

            khmc: khmc, //查询条件-客户
            bgr: reporter, //查询条件-报告人
            startdate: startdate, //查询条件-开始日期
            enddate: enddate, //查询条件-结束日期
            searchSort: "0",//查询时间顺序 0正序 1倒序(手机端默认正序)
        }
        this.setState({
            loading: true
        })
        let kh = util.getBrowserUrlParam("kh");
        let normal_display = "flex";
        Toast.loading('读取数据中...', 0);
        http.postAC(config.api_url, params, (result) => {
            console.log("获取销售报告数据result", result);
            Toast.hide()
            let data = [];
            if (result && result.status === true) {
                data = result.data;
            }

            if (kh && data.length > 0) {
                normal_display = "none";
                let params = {
                    fixed_khid: data[0].kh, //客户
                    fixed_khmc: data[0].khname,
                    fixed_manager: data[0].manager,
                    fixed_manager_name: data[0].managername,//经理
                    fixed_hyczname: data[0].hyczname,//行业产轴
                }
                that.props.setFixedBar(params);
            }

            that.setState({
                normal_display: normal_display,
                loading: false,
                allData: data,
                data: data.slice(0, pageSize),
                finished: data.length <= pageSize
            })
        });
    }


    handleScroll = () => {
        if (this.state.loading || this.state.finished || this.state.loadingmore) return;
        const container = document.getElementById('SD_Page');
        if (!container) return;
        const scrollTop = container.scrollTop;
        const clientHeight = container.clientHeight;
        const scrollHeight = container.scrollHeight;

        // 距离底部小于100px时自动加载
        if (scrollHeight - (scrollTop + clientHeight) < 200) {
            this.loadMore();
        }
    }

    loadMore = () => {
        if (this.state.loading || this.state.finished || this.state.loadingmore) return;
        this.setState({
            loadingmore: true,
        })
        setTimeout(() => {
            const {allData, data, page, pageSize} = this.state;
            const nextPage = page + 1;
            const nextData = allData.slice(0, nextPage * pageSize);
            this.setState({
                loadingmore: false,
                data: nextData,
                page: nextPage,
                finished: nextData.length >= allData.length
            });
        }, 300);
    }

    // 处理评论内容，确保 <br> 标签能正确显示
    processCommentContent = (content) => {
        if (!content) return '';

        // 处理可能被编码的 <br> 标签和全角字符
        let processedContent = content
            .replace(/&lt;br&gt;/gi, '<br>')
            .replace(/&lt;br\/&gt;/gi, '<br>')
            .replace(/&lt;br \/&gt;/gi, '<br>')
            .replace(/＜br＞/gi, '<br>')  // 全角尖括号
            .replace(/＜br\/＞/gi, '<br>')  // 全角尖括号
            .replace(/＜br \/＞/gi, '<br>')  // 全角尖括号
            .replace(/\n/g, '<br>'); // 将换行符也转换为 <br>

        return processedContent;
    }

    renderCard = (item, idx) => {
        const {normal_display} = this.state
        if (idx === 1) {
            console.log("1 item", item)
        }
        let gjjhname = item.gjjhname ? item.gjjhname : "(无)";

        // PC端布局，左右分栏，右侧为评论
        return (
            <div className="card-pc card-pc-flex" key={item.id}>
                <div className="card-index" title={item.id}>{idx + 1}</div>
                <div className="card-pc-left">
                    {/* 左侧第一行 */}
                    <div className="row" style={{
                        display: normal_display
                    }}>
                        <span className="label">客户名称</span>
                        <span className="value">
                                <a
                                    href={`/spa/crm/static/index.html#/main/crm/customerView?customerId=${item.kh}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    {item.khname}
                                </a>
                            </span>

                    </div>

                    <div className="row" style={{
                        display: normal_display
                    }}>
                        <span className="label">客户经理</span>
                        <span className="value">
                          <a href={`/spa/hrm/static4mobile/index.html#/resourceInfo/${item.manager}`}
                             target="_blank"
                             rel="noopener noreferrer">{item.managername}
                          </a>
                        </span>
                    </div>


                    <div className="row">
                        <span className="label">报告日期</span>
                        <span className="value">
                                <a
                                    href="#"
                                    onClick={e => {
                                        e.preventDefault();
                                        this.openSlide(item.id);
                                    }}
                                >
                                    {item.rq}
                                </a>
                            </span>
                    </div>

                    <div className="row">
                        <span className="label">报告人</span>
                        <span className="value">
                          <a href={`/spa/hrm/static4mobile/index.html#/resourceInfo/${item.tbr}`}
                             target="_blank"
                             rel="noopener noreferrer">{item.tbrname}
                          </a>
                        </span>
                    </div>
                    <div className="row">
                        <span className="label">标题</span>
                        <span className="value">
                                <a
                                    href="#"
                                    onClick={e => {
                                        e.preventDefault();
                                        this.openSlide(item.id);
                                    }}
                                >
                                    {item.bt}
                                </a>
                            </span>
                    </div>
                    {/* 行 */}
                    <div className="row" style={{
                        display: normal_display
                    }}>
                        <span className="label">行业-产轴</span>
                        <span className="value">{item.hyczname}</span>
                    </div>
                    {/*<div className="row"><span className="label">行业-精机</span><span*/}
                    {/*    className="value">{item.hyjjname}</span>*/}
                    {/*</div>*/}
                    {/* 行 */}
                    <div className="row">
                        <span className="label">涉及产品</span>
                        <span className="value" style={{
                            // maxHeight: "80px",
                            overflow: "auto"
                        }}>
                       {Array.isArray(item.chanpinList) && item.chanpinList.length > 0
                           ? item.chanpinList.map((d, idx) => (
                               <div key={d.id} className="desc-item">
                                   {d.name}
                               </div>
                           ))
                           : "(无)"}
                            </span>
                    </div>
                    {/* 行 */}
                    <div className="row">
                        <span className="label">拜访方式</span><span
                        className="value">{item.bffsname}</span>
                    </div>
                    {/* 行 */}
                    <div className="row">
                        <span className="label">详情描述</span>
                    </div>
                    <div className="value-area scrollable top-align high-desc">
                        {Array.isArray(item.detailDescList) && item.detailDescList.length > 0
                            ? item.detailDescList.map((d, idx) => (
                                <div key={d.id} className="desc-item">
                                    <div className="desc-jdname">{idx + 1}. {d.jdname}</div>
                                    <div className="desc-xxnr">{d.xxnr}</div>
                                </div>
                            ))
                            : "(无)"}
                    </div>
                    {/* 行 */}
                    <div className="row">
                        <span className="label">跟进计划</span>
                    </div>
                    <div className="value-area scrollable top-align high-plan">{gjjhname}</div>

                    <div className="row">
                        <span className="label">评论</span>
                    </div>
                    <div className="row value-area scrollable top-align comment-full-height">
                        {item.commentList && item.commentList.length > 0 ? item.commentList.map((c, i) => (
                            <div className="comment-item" key={i}>
                                <div className="comment-meta">
                                    <a href={`/spa/hrm/static4mobile/index.html#/resourceInfo/${item.replyor}`}
                                       target="_blank"
                                       rel="noopener noreferrer">{item.replyorname}
                                    </a>
                                    <span className="comment-date">{c.replydate} {c.replytime}</span>
                                </div>
                                <div className="comment-content"
                                     dangerouslySetInnerHTML={{__html: this.processCommentContent(c.replycontent)}}></div>
                            </div>
                        )) : "(无)"}

                    </div>

                    {/* 行 */}
                    <div className="row">
                        <span className="label">附件</span>
                        <span className="attach value">
                            {item.docList.map((c, i) => (
                                <div>
                                    <a
                                        href="#"
                                        onClick={e => {
                                            e.preventDefault();
                                            this.clickAttach(c, item);
                                        }}
                                    >
                                        {c.fileName}
                                    </a>
                                </div>

                            ))}
                            </span>
                    </div>
                </div>
            </div>
        )

    }

    /**
     * 侧滑打开建模卡片
     * @param attach
     * @param item
     */
    clickAttach = (attach, item) => {
        console.log("clickAttach", attach, item);
        let moduleid = config.report_modid;
        let url = "/spa/document/static4mobile/index.html#/attach/" + attach.fileid + "?formmode_authorize=formmode_authorize&moduleid=formmode&authorizemodeId=" + moduleid + "&authorizeformmodebillId=" + item.id;
        window.open(url);
    }
    /**
     * 侧滑打开建模卡片
     * @param billid
     */
    openSlide = (billid) => {
        let app_report_url = config.app_report_url;
        let url = app_report_url + "&billid=" + billid;
        window.open(url);
    }


    render() {
        const {hasData, data, finished, ismobile, allData, canLoadMore, visible_report, loading} = this.state;
        let showLoadMore = data.length < (allData ? allData.length : 0);

        let content = (
            <div className={"content"}>
                <div className={ismobile ? "card-list-mobile" : "card-list-pc"} ref={this.scrollRef}>
                    {visible_report && (
                        <div
                            style={{
                                position: 'fixed',
                                top: 0, left: 0, right: 0, bottom: 0,
                                zIndex: 10,
                                background: 'rgba(0,0,0,0.01)',
                            }}
                            onClick={this.onCloseSlide}
                        />
                    )}
                    {data.map(this.renderCard)}
                    {showLoadMore && <div className="list-end" ref={this.listEndRef}>继续下拉显示更多↓</div>}
                    {!showLoadMore && finished && <div className="list-end">-----已经到底了-----</div>}
                </div>
            </div>
        )

        let finalContent = hasData === true ? content : <NoData/>
        if (loading) {
            finalContent = (<div/>);
        }
        console.log("finalContent", finalContent)
        return (
            <div>{finalContent}</div>

        )
    }
}
