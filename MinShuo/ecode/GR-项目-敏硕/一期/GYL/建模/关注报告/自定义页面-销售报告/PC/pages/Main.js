const {WeaDateGroup, <PERSON>a<PERSON><PERSON><PERSON>, WeaLocaleProvider} = ecCom
const {Button} = antd;
const {getLabel} = WeaLocaleProvider;
const dataGroupDatas = [
    {name: '全部', value: '-1'},
    {name: '今年', value: '0'},
    {name: '去年', value: '1'},
    {name: '今天', value: '2'},
    {name: '本周', value: '3'},
    {name: '本月', value: '4'},
    {name: '本季', value: '5'},
    {name: '上个月', value: '7'},
    {name: '指定日期范围', value: '6'},

]
const defaultBrowserParams = {
    icon: "icon-toolbar-Organization-list",
    iconBgcolor: "#b32e37",
    hasAdvanceSerach: true,
    pageSize: 10,
    showCheckStrictly: true,
    selectedAllMaxLength: 500,
    displaySearchAdInBar: true,
    onBeforeFocusCheck: success => success(),
    placeholder: "请输入",
    isSingle: true,
};
const multiBrowserParams = {
    iconBgcolor: "#b32e37",
    hasAdvanceSerach: true,
    pageSize: 10,
    showCheckStrictly: true,
    selectedAllMaxLength: 500,
    displaySearchAdInBar: true,
    placeholder: "请输入",
    isSingle: false,
};

const singleCustomType = "browser.SelAllCustomter";

const img2Props = {
    src: "/cloudstore/release/${appId}/resources/xiaoshou_report.png",
    height: 32,
};

/**
 * 将时间范围转为 开始结束日期
 * @param arr 日期范围组件的值 例如 ['0']
 * @returns {{startDate: string, endDate: string}}
 */
function getDateRangeByValue(arr) {
    const type = arr[0];
    const today = new Date();
    let startDate = null, endDate = null;

    const getStartOfWeek = (date) => {
        const day = date.getDay() || 7;
        const diff = date.getDate() - day + 1;
        return new Date(date.getFullYear(), date.getMonth(), diff);
    };

    const getEndOfWeek = (date) => {
        const start = getStartOfWeek(date);
        return new Date(start.getFullYear(), start.getMonth(), start.getDate() + 6);
    };

    const getQuarter = (month) => Math.floor(month / 3);

    const getStartOfQuarter = (date) => {
        const quarter = getQuarter(date.getMonth());
        return new Date(date.getFullYear(), quarter * 3, 1);
    };

    const getEndOfQuarter = (date) => {
        const quarter = getQuarter(date.getMonth());
        return new Date(date.getFullYear(), quarter * 3 + 3, 0);
    };

    switch (type) {
        case '0': // 今年
            startDate = new Date(today.getFullYear(), 0, 1);
            endDate = new Date(today.getFullYear(), 11, 31);
            break;
        case '1': // 去年
            startDate = new Date(today.getFullYear() - 1, 0, 1);
            endDate = new Date(today.getFullYear() - 1, 11, 31);
            break;
        case '2': // 今天
            startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            break;
        case '3': // 本周
            startDate = getStartOfWeek(today);
            endDate = getEndOfWeek(today);
            break;
        case '4': // 本月
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            break;
        case '5': // 本季
            startDate = getStartOfQuarter(today);
            endDate = getEndOfQuarter(today);
            break;
        case '7': // 上个月
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            startDate = new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1);
            endDate = new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 0);
            break;
        case '6': // 指定日期范围
            if (arr.length === 3 && arr[1] && arr[2]) {
                const s = new Date(arr[1]);
                const e = new Date(arr[2]);
                if (!isNaN(s.getTime()) && !isNaN(e.getTime())) {
                    startDate = s;
                    endDate = e;
                }
            }
            break;
    }

    const format = (date) => {
        if (!date) return '';
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        return `${y}-${m}-${d}`;
    };

    return {
        startDate: format(startDate),
        endDate: format(endDate)
    };
}


class Main extends React.Component {
    constructor(props) {
        super(props);
        let currentYear = new Date().getFullYear() + "";
        this.state = {
            searchSort: "1",//默认按照时间正序查询数据，0正序，1倒序
            buttonAscLoading: false,
            buttonLoading: false,
            currentYear: currentYear,
            khmc: "",
            reporter: "",
            startDate: "",
            endDate: "",
            dataGroupValue: ["-1"], //默认全部
            kh_viewAttr: 2,
            fixed_khid: "",//固定客户id
            fixed_khmc: "",//固定客户名称
            fixed_manager: "",//固定客户经理id
            fixed_manager_name: "",//固定客户经理名称
            fixed_hyczname: "",//固定行业产轴
            fixed_display: "none",
            normal_display: "flex",
        }
    }

    componentWillMount() {
    }

    componentDidMount() {
        let that = this;
        //判断是否有url参数
        const {util} = window.GRSDK
        let kh = util.getBrowserUrlParam("kh");
        let hidetitle = util.getBrowserUrlParam("hidetitle");

        let khname = "";
        let customerData = [];
        let kh_viewAttr = 2;
        let display_title = "flex";
        if (hidetitle && hidetitle + "" === "1") {
            display_title = "none";
        }
        if (kh) {
            khname = this.getCustomerName(kh);
            kh_viewAttr = 1;
            customerData = [{
                id: kh,
                name: khname,
            }]
        }
        this.setState({
            kh_viewAttr: kh_viewAttr,
            khmc: kh,
            display_title: display_title,
            customerData: customerData
        }, () => {
            that.refreshAll();
        })
    }

    getCustomerName = (kh) => {
        const {db} = window.GRSDK;
        let sql = "select name from crm_customerinfo where id =" + kh;
        let result = db.query(sql);
        if (result && result.data && result.data.length > 0) {
            return result.data[0].name;
        }
        return "";
    }

    /**
     * 设置子页面的ref，将子页面的this传递过来，方便当前页面调用
     * @param name
     * @returns {(function(*): void)|*}
     */
    setChildRef = (name) => (ref) => {
        this[name] = ref;
    }


    refreshAll = () => {
        const {khmc, reporter, startDate, endDate, dataGroupValue, searchSort} = this.state;
        console.log("refreshAll", startDate, endDate);
        if (this.content) {
            this.content.refresh(khmc, reporter, startDate, endDate, searchSort);
        }
    }

    /**
     * 正序查看搜索
     */
    clickRefreshAsc = () => {
        let that = this;
        this.setState({
            buttonAscLoading: true,
            searchSort: "0"
        }, () => {
            that.refreshAll();
            this.setState({buttonAscLoading: false});
        });
    }

    /**
     * 倒序搜索
     */
    clickRefresh = () => {
        let that = this;
        this.setState({
            buttonLoading: true,
            searchSort: "1"
        }, () => {
            that.refreshAll();
            this.setState({buttonLoading: false});
        });
    }

    /**
     * 选择提交人
     * @param ids
     * @param names
     * @param datas
     */
    selectPerson = (ids, names, datas) => {
        let that = this;
        console.log("选择提交人：", datas);
        this.setState({
            reporter: ids,
        }, () => {
            that.refreshAll();
        })
    }
    /**
     * 选择客户
     * @param ids
     * @param names
     * @param datas
     */
    selectCustomer = (ids, names, datas) => {
        let that = this;
        console.log("选择客户：", datas);
        this.setState({
            khmc: ids,
            customerData: datas
        }, () => {
            that.refreshAll();
        })
    }
    /**
     * 选择日期范围
     * @param value
     */
    selectDateGroup = (value) => {
        let that = this;
        console.log("选择日期范围", value);
        let dataRange = getDateRangeByValue(value);
        console.log("选择日期范围解析", dataRange);

        this.setState({
            dataGroupValue: value,
            startDate: dataRange.startDate,
            endDate: dataRange.endDate,
        }, () => {
            //如果没有解析出来范围，则不刷新
            //选择范围的时候
            if (value[0] + "" === "6") {
                if (dataRange.startDate && dataRange.endDate) {
                    that.refreshAll();
                }
            } else {
                that.refreshAll();
            }

        })
    }

    setFixedBar = (params) => {
        this.setState({
            normal_display: "none",
            fixed_display: "flex",
            fixed_khid: params.fixed_khid,
            fixed_khmc: params.fixed_khmc,
            fixed_manager: params.fixed_manager,
            fixed_manager_name: params.fixed_manager_name,
            fixed_hyczname: params.fixed_hyczname,
        })
    }

    /**
     * 渲染
     *
     */
    render() {
        const {
            dataGroupValue, buttonAscLoading, buttonLoading, customerData, kh_viewAttr,
            fixed_display, normal_display,
            fixed_khid, fixed_khmc, fixed_manager, fixed_manager_name, fixed_hyczname,
            display_title
        } = this.state;
        return (
            <div className={"SD_Page"}>
                <div className={"top-toolbar"}>
                    <div className={"title"} style={{
                        display: display_title
                    }}>
                        <img {...img2Props} />
                        <span>销售报告</span>
                    </div>
                    <div className={"search"}>
                        <div className="condition">
                            <div style={{
                                display: normal_display,
                                alignItems: "center"
                            }}>
                             <span className={"searchName"}>
                            客户
                        </span>
                                <WeaBrowser
                                    type={161}
                                    title="选择客户"
                                    completeParams={{type: 161, fielddbtype: singleCustomType}}
                                    conditionDataParams={{type: singleCustomType}}
                                    dataParams={{
                                        type: singleCustomType,
                                    }}
                                    destDataParams={{type: singleCustomType}}
                                    {...defaultBrowserParams}
                                    inputStyle={{width: 120}}
                                    style={{
                                        background: "white",
                                    }}
                                    replaceDatas={customerData}
                                    linkUrl="/spa/crm/static/index.html#/main/crm/customerView?customerId="
                                    viewAttr={kh_viewAttr}
                                    onChange={this.selectCustomer}

                                />
                            </div>

                            <span className={"searchName"}>
                            提交人
                        </span>
                            <WeaBrowser
                                type={1}
                                title="人力资源"
                                tabs={[
                                    {
                                        name: getLabel(24515, "最近"),
                                        key: "1"
                                    },
                                    {
                                        name: getLabel(18511, "同部门"),
                                        key: "2"
                                    },
                                    {
                                        name: getLabel(15089, "我的下属"),
                                        key: "3"
                                    },
                                    {
                                        name: getLabel(18770, "按组织结构"),
                                        key: "4",
                                        browserProps: {
                                            browserTreeCustomProps: {
                                                defaultExpandedLevel: 2
                                            }
                                        }
                                    },
                                    {
                                        name: getLabel(81554, "常用组"),
                                        key: "5"
                                    },
                                    {
                                        name: "所有人",
                                        key: "6"
                                    }
                                ]}
                                showDls
                                linkUrl="/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/"
                                isSingle={true}
                                inputStyle={{width: 120}}
                                onChange={this.selectPerson}
                            />
                            <span className={"searchName"}>
                            报告日期
                        </span>
                            <WeaDateGroup
                                isMobx
                                isInline
                                value={dataGroupValue}
                                datas={dataGroupDatas}
                                onChange={this.selectDateGroup}
                                style={{
                                    width: "120px",
                                    background: "white",
                                    marginLeft: "10px"
                                }}
                            />
                        </div>
                        <div className={"button_area"}>
                            <Button type="primary" icon="reload" loading={buttonAscLoading}
                                    onClick={this.clickRefreshAsc}>
                                正序查看
                            </Button>
                            <Button type="primary" icon="reload" loading={buttonLoading}
                                    onClick={this.clickRefresh}>
                                搜索
                            </Button>
                        </div>

                    </div>
                </div>
                <div className={"fixed_field_bar"} style={{
                    display: fixed_display
                }}>
                    <div className="fixed_field">
                        <span className="label">客户名称：</span>
                        <span className="value">
                                <a
                                    href={`/spa/crm/static/index.html#/main/crm/customerView?customerId=${fixed_khid}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    {fixed_khmc}
                                </a>
                            </span>
                    </div>
                    <div className="fixed_field">
                        <span className="label">客户经理：</span>
                        <span className="value">
                                <WeaBrowser
                                    type={1}
                                    title="人力资源"
                                    showDls
                                    viewAttr={1}
                                    replaceDatas={[{id: fixed_manager, name: fixed_manager_name}]}
                                    linkUrl="/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/"
                                    {...defaultBrowserParams}
                                />
                            </span>
                    </div>
                    <div className="fixed_field">
                        <span className="label">行业-产轴：</span>
                        <span className="value">{fixed_hyczname}</span>
                    </div>

                </div>
                <Content {...this.props} setPageRef={this.setChildRef("content")} setFixedBar={this.setFixedBar}/>

            </div>

        )
    }
}