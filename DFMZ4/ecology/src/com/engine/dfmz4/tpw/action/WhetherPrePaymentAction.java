package com.engine.dfmz4.tpw.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.BaseSDAction;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;

/**
 * @FileName WhetherPreReceiptAction
 * @Description //是否预付
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/25
 */
public class WhetherPrePaymentAction extends BaseSDAction implements Action {

    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public static  boolean isNextOrFutureMonth(String yearMonthStr) {
        try {
            // 解析输入字符串
            YearMonth inputMonth = YearMonth.parse(yearMonthStr, DateTimeFormatter.ofPattern("yyyy-MM"));

            // 获取下个月
            YearMonth nextMonth = YearMonth.now().plusMonths(1);

            // 检查是否 >= 下个月
            return !inputMonth.isBefore(nextMonth);
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    @Override
    public String execute(RequestInfo requestInfo) {

        //SDAction初始化
        initAction(requestInfo, this.getClass(), null, "是否预付");
        if (getActionError().isEmpty()) {
            try {
                if (getActionError().isEmpty()) {
                    //执行业务逻辑
                    appendLog("WhetherPrePaymentAction执行主方法executeMy()开始");
                    executeMy();
                    appendLog("WhetherPrePaymentAction执行主方法executeMy()结束");
                }
            } catch (Exception e) {
                getThreadLocalBaseParam().actionError = "action执行异常:" + SDUtil.getExceptionDetail(e);
                appendLog(getActionError());
                log.error("WhetherPrePaymentAction执行异常:", e);
            } finally {
                appendLog(this.getClass().getName() + "---END---requestid:" + requestInfo.getRequestid());
            }
        }
        return actionReturn();

    }

    private void executeMy() {
        try {
            String formtableName = getThreadLocalBaseParam().actionInfo.getFormtableName();
            Map<Integer, List<Map<String, String>>> detailData = getThreadLocalBaseParam().actionInfo.getDetailData();
            List<Map<String, String>> detailDataList1 = detailData.get(1);
            appendLog("detailDataList1: "+ JSONObject.toJSONString(detailDataList1));
            if (detailDataList1 != null && !detailDataList1.isEmpty()) {
                for (Map<String, String> detail : detailDataList1) {
                    RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                    int type = 0;
                    String id = Util.null2String(detail.get("id"));
                    //自定义执行逻辑 是否结转收入
                    //收入确认类型
                    String cwlyjd = Util.null2String(detail.get("cwlyjd"));
                    if (StringUtils.isNotBlank(cwlyjd)) {
                        recordSet.executeQuery("select * from uf_zchtcwsjda_dt4 where id = ?", cwlyjd);
                        //支出确认类型
                        String srqrlx = recordSet.getString("srqrlx");
                        //财务所属时间
                        String cwssrq = recordSet.getString("cwszsj");
                        //计提时间
                        String kp = recordSet.getString("jtsj");
                        //支付时间
                        String sk = recordSet.getString("zfsj");
                        if (StringUtils.isNotBlank(srqrlx)) {
                            if ("2".equals(srqrlx) || "3".equals(srqrlx)) {
                                type = 0;
                            } else if("1".equals(srqrlx)){
                                if (StringUtils.isBlank(kp) && StringUtils.isBlank(sk)) {
                                    type = 1;
                                } else if (StringUtils.isNotBlank(cwssrq)) {
                                    String yearMonth1 = cwssrq.substring(0, 7);
                                    if (StringUtils.isBlank(kp) && StringUtils.isNotBlank(sk)) {
                                        String yearMonth3 = sk.substring(0, 7);
                                        int result = yearMonth1.compareTo(yearMonth3);
                                        if (result <= 0) {
                                            type = 1;
                                        }
                                    } else if (StringUtils.isNotBlank(kp) && StringUtils.isBlank(sk)) {
                                        String yearMonth2 = kp.substring(0, 7);
                                        int result = yearMonth1.compareTo(yearMonth2);
                                        if (result <= 0) {
                                            type = 1;
                                        }
                                    } else if (StringUtils.isNotBlank(kp) && StringUtils.isNotBlank(sk)) {
                                        String yearMonth2 = kp.substring(0, 7);
                                        String yearMonth3 = sk.substring(0, 7);
                                        int result = yearMonth2.compareTo(yearMonth3);
                                        if (result < 0) {
                                            int result1 = yearMonth1.compareTo(yearMonth2);
                                            if (result1 <= 0) {
                                                type = 1;
                                            }
                                        } else {
                                            int result1 = yearMonth1.compareTo(yearMonth3);
                                            if (result1 <= 0) {
                                                type = 1;
                                            }
                                        }
                                    }
                                }
                            }else if("0".equals(srqrlx)){
                                if (StringUtils.isNotBlank(kp) && StringUtils.isBlank(sk)) {
                                    type = 1;
                                }else if(StringUtils.isBlank(kp) && StringUtils.isNotBlank(sk)){
                                    String yearMonth3 = sk.substring(0, 7);
                                    if(isNextOrFutureMonth(yearMonth3)){
                                        type = 0;
                                    }else {
                                        type = 1;
                                    }
                                }
                            }
                        }
                    }
                    //更新明细表
                    String sql = "update " + formtableName + "_dt1 set sfyf = ?  where  id = ?";
                    appendLog(sql +" type："+type + " id："+id);
                    recordSet.executeUpdate(sql, type, id);
                    DBUtil.clearThreadLocalRecordSet();
                }
            }
        } catch (Exception e) {
            getThreadLocalBaseParam().actionError = "execuetMy异常：" + SDUtil.getExceptionDetail(e);
            appendLog(getThreadLocalBaseParam().actionError);
        }
    }


}
