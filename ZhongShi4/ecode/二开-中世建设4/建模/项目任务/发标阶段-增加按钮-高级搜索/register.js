let cfgParam
let isAddButtonEvent = false
let needChangeSize = false;
let needRun = false;
let search_dw = "";
let search_bjh = "";

/**
 * 执行主方法，方法名固定
 * @param newProps 组件参数，可对其更改
 * @param params 主控的配置信息
 */
function execute(newProps, params) {
    cfgParam = params
    needRun = true;
    //以下为需要多次执行的逻辑
    //渲染自定义明细页面
    let limitTime = 5 * 1000; //设置加载延迟最大值 单位毫秒
    let startTime = new Date();
    let t1 = window.setInterval(function () {
        let currentTime = new Date();
        let diffTime = currentTime - startTime;
        //超过限制延时
        if (diffTime > limitTime) {
            console.warn("超过延迟最大时间，未找到custombutton2-2元素id");
            window.clearInterval(t1);
        } else {
            // 获取现建模上配置的id元素
            let existingElement = document.getElementById('custombutton2-2')
            if (existingElement) {
                window.clearInterval(t1);
                //添加自定义按钮
                addCustomButton()

            }
        }
    }, 100);
}

//添加自定义按钮
const addCustomButton = () => {
    let btnLength = $("#btn2_2_adsearch").length
    if (btnLength * 1 > 0) return
    $(".custombutton").css({
        'display': 'flex',
        'float': 'right',
    })
    //发标信息
    //高级搜索按钮
    let buttonDiv = '<div style="margin:10px" class="custombtnEdit" id="btn2_2_adsearch"><button  class="ant-btn ant-btn-primary" style="font-style:initial">高级搜索</button></div>'
    $("#custombutton2-2").prepend(buttonDiv)
    console.log("二开-发标阶段-添加高级搜索按钮")
    //按钮事件只能添加一次，否则会出现点一次多次执行的问题
    if (!isAddButtonEvent) {
        $(document).on("click", "#btn2_2_adsearch", function (event) {
            btnClick()
        })
        isAddButtonEvent = true
    }

}

//保证金流程按钮
function btnClick() {
    ecCom.WeaTools.createDialog({
        title: '高级搜索',
        url: "/spa/custom/static/index.html#/main/cs/app/${appId}_page",
        icon: "icon-coms-workflow",
        iconBgcolor: "#0079DE",
        style: {width: "400px", height: 200},
        callback: (params) => { // 数据通信
            onOk(params);
        },
        onCancel: () => { // 关闭通信
        }
    }, undefined, (dialog) => {
        // 由于组件异步化可能导致第一次没有加载到组件，所以需要在回调中调用
        dialog.show();
    });
}

function onOk(params) {
    console.log("onOk params", params);
    search_dw = params.dwmc;
    search_bjh = params.bjh;

    if (search_dw || search_bjh) {
        needChangeSize = true; //需要修改当前分页大小，为最大，只显示一页数据的效果
    } else {
        needChangeSize = false;
    }
    //处理显示和隐藏明细行
    doHideDetailData();

    //需要重新load数据
    // modeForm.getCurrentModeStore().reloadCardDatas();
    // setTimeout(function () {
    //     //设置延迟，每100毫秒执行一次，满足条件后，删除t1延时器
    //     let limitTime = 60 * 1000; //设置加载延迟最大值 单位毫秒
    //     let startTime = new Date();
    //     let t1 = window.setInterval(function () {
    //         let currentTime = new Date();
    //         let diffTime = currentTime - startTime;
    //         //超过限制延时
    //         if (diffTime > limitTime) {
    //             window.clearInterval(t1);
    //             console.log("超时没有获取到明细2数据")
    //         } else {
    //             let detailCount = ModeForm.getDetailRowCount("detail_2");
    //             if (detailCount > 0) {
    //                 window.clearInterval(t1);
    //                 doHideDetailData();
    //             }
    //         }
    //     }, 100);
    // }, 500)
}

function doHideDetailData() {
    let detailStr = "detail_2";
    let fi_dwmc = ModeForm.convertFieldNameToId("dwmc", detailStr);//单位名称
    let fi_bjh = ModeForm.convertFieldNameToId("bjh", detailStr);//包件号
    //遍历明细行标,获取明细行各个数据
    let rowArr = ModeForm.getDetailAllRowIndexStr(detailStr).split(",");
    let showRowIndexArray = [];
    let hideRowIndexArray = [];
    for (let i = 0; i < rowArr.length; i++) {
        let rowIndex = rowArr[i];
        if (rowIndex !== "") {
            let fm = fi_dwmc + "_" + rowIndex;
            let fv_dwmc = ModeForm.getFieldValue(fm) + "";

            fm = fi_bjh + "_" + rowIndex;
            let fv_bjh = ModeForm.getFieldValue(fm) + "";

            let match_dwmc = checkMatch(search_dw || "", fv_dwmc);
            let match_bjh = checkMatch(search_bjh || "", fv_bjh);
            console.log(rowIndex + "单位名称匹配：", match_bjh);
            console.log(rowIndex + "包件号匹配：", match_dwmc);

            let element = $('#oTable2 tr[data-rowindex="' + rowIndex + '"]');
            if (match_dwmc && match_bjh) {
                //element.show();
                showRowIndexArray.push(rowIndex);

            } else {
                //element.hide();
                hideRowIndexArray.push(rowIndex);

            }
        }
    }
    console.log("show indexs ", showRowIndexArray.join(","))
    if (showRowIndexArray.length > 0) {
        ModeForm.controlDetailRowDisplay(detailStr, showRowIndexArray.join(","), false);   //显示
    }
    if (hideRowIndexArray.length > 0) {
        ModeForm.controlDetailRowDisplay(detailStr, hideRowIndexArray.join(","), true);   //隐藏
    }
}

/**
 * 判断实际值，是否匹配搜索值，类似数据库的 实际值like '%搜索值%'
 * @param searchValue 搜索值
 * @param value 数据库存的实际值
 *  @returns {boolean} 是否匹配
 */
function checkMatch(searchValue, value) {
    // 转换为字符串（保留原始大小写）
    const searchStr = String(searchValue).trim();
    const valueStr = String(value).trim();

    // 空搜索字符串匹配所有记录（与SQL行为一致）
    if (searchStr === '') {
        return true;
    }

    // 严格区分大小写的包含匹配
    return valueStr.includes(searchStr);
}

/**
 * 拦截
 */
// ecodeSDK.rewriteApiDataQueueSet({
//     fn: (url, params, data) => {
//         if (!needRun) return
//         if (url.indexOf('/api/cube/new/card/getDetailFields') < 0) return
//         if (params.tablename !== "uf_cwcc_xmrw_dt2") return
//         if (!data.datas) return
//         if (search_dw || search_bjh) {
//             let datas = data.datas;
//             for (let i = 0; i < datas.length; i++) {
//                 let fv_dw = datas[i].field8322;
//                 let fv_bjh = datas[i].field8320;
//                 let match_dwmc = checkMatch(search_dw || "", fv_dw);
//                 let match_bjh = checkMatch(search_bjh || "", fv_bjh);
//                 if (match_dwmc && match_bjh) {
//                     element.show();
//                 } else {
//                     datas.splice(i, 1);
//                     i--;
//                 }
//             }
//         }
//
//         return data
//     }
// })


//导出到window全局的sdModuleFunc对象中,固定语句
if (window.sdModuleFunc) {
    window.sdModuleFunc['${appId}'] = execute
} else {
    window.sdModuleFunc = {
        '${appId}': execute,
    }
}