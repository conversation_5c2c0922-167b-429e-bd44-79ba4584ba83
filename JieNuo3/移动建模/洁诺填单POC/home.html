<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>表单中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        html,
        body {
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        body {
            background-color: #e6ecf2;
            color: #333;
            line-height: 1.5;
            -webkit-overflow-scrolling: touch;
        }

        .container {
            width: 100%;
            height: 100%;
            padding: 0;
            background: #f0f0f0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* 顶部搜索栏 */
        .search-bar {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #fff;
            padding: 18px 20px 12px 20px;
            box-shadow: 0 4px 16px rgba(24, 144, 255, 0.08);
        }

        .search-box {
            display: flex;
            align-items: center;
            background-color: #f1f3f6;
            border-radius: 22px;
            padding: 10px 18px;
            width: 100%;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.04);
        }

        .search-box input {
            flex: 1;
            border: none;
            background: transparent;
            outline: none;
            font-size: 15px;
            padding: 4px 0;
            -webkit-appearance: none;
            appearance: none;
            user-select: text;
            -webkit-user-select: text;
        }

        .search-box input:focus {
            outline: none;
        }

        .search-icon {
            color: #1890ff;
            margin-right: 10px;
            font-size: 18px;
        }

        /* Tab切换 - 优化设计 */
        .tab-container {
            display: flex;
            background-color: #fff;
            position: sticky;
            top: 64px;
            z-index: 20;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.06);
            border-radius: 0 0 16px 16px;
            margin-bottom: 8px;
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 14px 0;
            font-size: 16px;
            font-weight: 500;
            color: #b0b0b0;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            border-bottom: 3px solid transparent;
        }

        .tab-item.active {
            color: #1890ff;
            border-bottom: 3px solid #1890ff;
            background: none;
        }

        /* 分类标题 */
        .category-title {
            font-size: 17px;
            font-weight: 600;
            color: #333;
            margin: 32px 20px 14px 20px;
            display: flex;
            align-items: center;
            letter-spacing: 1px;
        }

        .category-title:before {
            content: '';
            display: inline-block;
            width: 5px;
            height: 18px;
            background-color: #1890ff;
            margin-right: 10px;
            border-radius: 2px;
        }

        /* 表单列表 - 缩小卡片并增加圆角 */
        .form-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            width: 100%;
            margin: 0 0 20px 0;
            padding: 0 12px;
            align-items: stretch;
        }

        .form-card {
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 16px rgba(24, 144, 255, 0.13);
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
            min-width: 0;
            word-break: break-all;
            font-size: 13px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            aspect-ratio: 1 / 0.8;
            padding: 12px 8px;
            justify-content: flex-start;
        }

        .form-card:hover {
            transform: translateY(-3px) scale(1.03);
            box-shadow: 0 6px 18px rgba(24, 144, 255, 0.13);
        }

        .form-icon {
            font-size: 16px;
            width: 24px;
            height: 24px;
            background-color: #e6f7ff;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 6px;
            color: #1890ff;
        }

        .form-name {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 6px;
            color: #222;
        }

        .form-desc {
            font-size: 13px;
            color: #999;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        /* 暂无数据样式 - 添加白色背景并居中 */
        .no-data {
            text-align: center;
            padding: 28px 0;
            color: #b0b0b0;
            font-size: 15px;
            background-color: #fff;
            border-radius: 14px;
            grid-column: 1 / -1;
            margin: 0;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.05);
        }

        /* 搜索匹配结果样式 */
        .search-matched {
            display: none;
            background-color: #fff;
            border-radius: 12px;
            margin: 16px 20px;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.07);
        }

        .search-matched-title {
            font-size: 17px;
            font-weight: 600;
            color: #333;
            padding: 18px 18px 10px 18px;
            border-bottom: 1px solid #f0f0f0;
        }

        /* 响应式调整 */
        /* @media (max-width: 500px) {
            .form-list {
                grid-template-columns: 1fr;
            }
            .search-bar, .tab-container, .category-title, .form-list, .search-matched {
                margin-left: 0;
                margin-right: 0;
                padding-left: 8px;
                padding-right: 8px;
            }
        } */

        .tab-content {
            width: 100%;
        }

        .search-group {
            margin-bottom: 18px;
        }

        .form-card-empty {
            background: transparent;
            box-shadow: none;
            border: none;
            cursor: default;
        }

        .search-history-section {
            margin-top: 12px;
            background-color: #fff;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /*  subtle 阴影 */
        }

        /* 头部：标题（历史搜索）+ 删除按钮 左右分布 */
        .history-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        /* 单 Tab 作为标题：放大字号 + 加粗，移除切换下划线 */
        .history-tabs {
            /* 无需 flex（仅一个项） */
        }

        .tab-item {
            font-size: 16px; /* 标题字号更大 */
            font-weight: 600; /* 标题加粗 */
            color: #333; /* 标题深色 */
        }

        /* 移除 Tab 切换的下划线（因无切换需求） */
        .tab-item.active::after {
            display: none;
        }

        /* 删除全部按钮：保留原有 class，调整样式 */
        .clear-all-btn {
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 18px; /* 图标稍大 */
            color: #999; /* 默认灰色 */
            transition: color 0.2s;
        }

        .clear-all-btn:hover {
            color: #ff4d4f; /* 悬浮变红，提示删除 */
        }

        /* 标签列表：自动换行 + 间距，模仿参考图 */
        .tags-list {
            display: flex;
            flex-wrap: wrap; /* 超出自动换行 */
            gap: 8px; /* 标签间距 */
        }

        /* 单个历史标签：圆角 + 浅灰边框 + 悬浮高亮 */
        .tags-list li {
            list-style: none;
            padding: 4px 10px;
            border: 1px solid #e5e5e5; /* 浅灰边框 */
            border-radius: 18px; /* 大圆角，匹配参考图 */
            color: #666;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s, color 0.2s;
            white-space: nowrap; /* 防止文字换行 */
        }

        .tags-list li:hover {
            background-color: #fafafa; /* 悬浮浅灰背景 */
            color: #333; /* 悬浮深色文字 */
        }

        /* 无历史时的提示（保留原有 class） */
        .no-history-tip {
            color: #999;
            font-size: 12px;
        }

        /* 隐藏 Tab 容器，直接用文字标题 */
        .history-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        /* 新增“历史搜索”标题（替代 Tab） */
        .history-header::before {
            content: '历史搜索';
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        /* 隐藏原 Tab 元素（仅 HTML 保留，CSS 隐藏） */
        .history-tabs {
            display: none;
        }
    </style>
</head>

<body>
<div class="container">
    <!-- 顶部搜索栏 - 调整宽度 -->
    <div class="search-bar">
        <div class="search-box">
            <span class="search-icon">🔍</span>
            <input type="text" id="searchInput" placeholder="搜索表单名称或关键词..." autocomplete="off">
        </div>
        <div class="search-history-section">
            <div class="history-header">
                <div class="history-tabs">
                    <!-- 仅保留“历史搜索”Tab，active 类保留（无需切换，作为标题） -->
                    <span class="tab-item active">历史搜索</span>
                </div>
                <button class="clear-all-btn">
                    <svg class="search-suggest-menu-history-clean-icon" xmlns="http://www.w3.org/2000/svg"
                         fill="none" version="1.1" width="11.14288330078125"
                         height="10.857177734375" viewBox="0 0 11.14288330078125 10.857177734375">
                        <g>
                            <path d="M7.28573,0.857143L3.85716,0.857143C3.62047,0.857143,3.42859,0.665265,3.42859,0.428571C3.42859,0.191878,3.62047,0,3.85716,0L7.28573,0C7.52243,0,7.7143,0.191878,7.7143,0.428571C7.7143,0.665265,7.52243,0.857143,7.28573,0.857143ZM1.14282,2.85714L0.428571,2.85714C0.191878,2.85714,0,2.66527,0,2.42857C0,2.19188,0.191878,2,0.428571,2L10.7143,2C10.951,2,11.1429,2.19188,11.1429,2.42857C11.1429,2.66527,10.951,2.85714,10.7143,2.85714L9.99997,2.85714L9.99997,7Q9.99997,7.94217,9.97551,8.28879Q9.93623,8.84548,9.78649,9.19951Q9.35722,10.2144,8.34233,10.6437Q7.9883,10.7934,7.43162,10.8327Q7.08499,10.8571,6.14282,10.8571L4.99997,10.8571Q4.0578,10.8571,3.71117,10.8327Q3.15449,10.7934,2.80045,10.6437Q1.78556,10.2144,1.3563,9.19951Q1.20656,8.84548,1.16728,8.28879Q1.14282,7.94217,1.14282,7L1.14282,2.85714ZM1.99997,2.85714L1.99997,7Q1.99997,8.52098,2.14573,8.86561Q2.43959,9.56037,3.13436,9.85423Q3.47899,10,4.99997,10L6.14282,10Q7.6638,10,8.00843,9.85423Q8.7032,9.56037,8.99706,8.86561Q9.14282,8.52098,9.14282,7L9.14282,2.85714L1.99997,2.85714ZM3.71423,8.14291L3.71423,4.71434C3.71423,4.47764,3.90611,4.28577,4.14281,4.28577C4.3795,4.28577,4.57138,4.47764,4.57138,4.71434L4.57138,8.14291C4.57138,8.3796,4.3795,8.57148,4.14281,8.57148C3.90611,8.57148,3.71423,8.3796,3.71423,8.14291ZM6.57141,8.14291L6.57141,4.71434C6.57141,4.47764,6.76329,4.28577,6.99998,4.28577C7.23668,4.28577,7.42855,4.47764,7.42855,4.71434L7.42855,8.14291C7.42855,8.3796,7.23668,8.57148,6.99998,8.57148C6.76329,8.57148,6.57141,8.3796,6.57141,8.14291Z"
                                  fill-rule="evenodd" fill="#7A7A7A" fill-opacity="1"></path>
                        </g>
                    </svg>
                    ️
                </button>
            </div>

            <div class="history-tags">
                <ul class="tags-list" id="tagsList"></ul>
            </div>
        </div>
    </div>

    <!-- Tab切换 - 优化设计 -->
    <div class="tab-container">
        <div class="tab-item active" data-tab="operation">运营</div>
        <div class="tab-item" data-tab="quality">质量</div>
    </div>

    <!-- 搜索匹配结果区域 -->
    <div class="search-matched" id="searchMatched">
        <div class="search-matched-title">搜索结果</div>
        <div class="form-list" id="matchedForms"></div>
    </div>

    <!-- 原始表单分类 -->
    <div id="originalForms">

        <!-- 运营分类 -->
        <div class="tab-content active" id="operation-content">
            <!-- 通用分类 -->
            <h2 class="category-title">多区域通用</h2>
            <div class="form-list">

                <div class="form-card" onclick="navigateToForm('医用封口机检查测试记录表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">医用封口机检查测试记录表</h3>
                    <p class="form-desc">XCWI034-F01A</p>
                </div>

            </div>

            <!-- 去污分类 -->
            <h2 class="category-title">去污</h2>
            <div class="form-list">
                <div class="form-card" onclick="navigateToForm('清洗消毒器')">
                    <div class="form-icon">🧼</div>
                    <h3 class="form-name">清洗消毒器</h3>
                    <p class="form-desc">清洗消毒器使用记录表</p>
                </div>
                <div class="form-card" onclick="navigateToForm('去污区物表清洁及消毒记录')">
                    <div class="form-icon">🧽</div>
                    <h3 class="form-name">去污区物表清洁及消毒记录</h3>
                    <p class="form-desc">去污区物体表面清洁消毒记录表</p>
                </div>
                <div class="form-card" onclick="navigateToForm('（酒精）危化品使用登记表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">（酒精）危化品使用登记表</h3>
                    <p class="form-desc">（酒精）危化品使用登记表</p>
                </div>
                <div class="form-card" onclick="navigateToForm('超声清洗机检查及配液配置记录表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">超声清洗机检查及配液配置记录表</h3>
                    <p class="form-desc">XCWI030-F01A1</p>
                </div>
                <div class="form-card" onclick="navigateToForm('煮沸消毒器使用前检查及日常清洁保养表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">煮沸消毒器使用前检查及日常清洁保养表</h3>
                    <p class="form-desc">AHWI075-F01/A0</p>
                </div>
                <div class="form-card" onclick="navigateToForm('紫外线消毒记录')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">紫外线消毒记录</h3>
                    <p class="form-desc">AHWI028-F14A1</p>
                </div>
                <div class="form-card" onclick="navigateToForm('酸化水pH、有效氯检测记录表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">酸化水pH、有效氯检测记录表</h3>
                    <p class="form-desc">XCWI040-F01A2-01/02</p>
                </div>
                <div class="form-card" onclick="navigateToForm('去污区辅助设备清洁及点检记录表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">去污区辅助设备清洁及点检记录表</h3>
                    <p class="form-desc">XCWI030-F03A</p>
                </div>
                <div class="form-card" onclick="navigateToForm('清洗层架检查记录表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">清洗层架检查记录表</h3>
                    <p class="form-desc">XCWI104-F01A</p>
                </div>
                <div class="form-card" onclick="navigateToForm('内镜工作站使用后清洁消毒登记表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">内镜工作站使用后清洁消毒登记表</h3>
                    <p class="form-desc">XCWI084-F01A0</p>
                </div>
                <div class="form-card" onclick="navigateToForm('水池清洗剂、除锈剂配置记录表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">水池清洗剂、除锈剂配置记录表</h3>
                    <p class="form-desc">XCWI094-F01A</p>
                </div>

            </div>

            <!-- 检查包装分类 -->
            <h2 class="category-title">检查包装</h2>
            <div class="form-list">
                <div class="form-card" onclick="navigateToForm('绝缘检测仪每日点检记录表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">绝缘检测仪每日点检记录表</h3>
                    <p class="form-desc">XCWI041-F01A</p>
                </div>
                <div class="form-card" onclick="navigateToForm('检查包装区设备清洁保养记录')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">检查包装区设备清洁保养记录</h3>
                    <p class="form-desc">XCWI009-F05B</p>
                </div>
                <div class="form-card" onclick="navigateToForm('检查包装区物表清洁及消毒记录')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">检查包装区物表清洁及消毒记录</h3>
                    <p class="form-desc">AHWI028-F03A1</p>
                </div>

            </div>

            <!-- 敷料分类 -->
            <h2 class="category-title">敷料</h2>
            <div class="form-list">

                <div class="form-card" onclick="navigateToForm('敷料区物表清洁及消毒记录')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">敷料区物表清洁及消毒记录</h3>
                    <p class="form-desc">AHWI028-F05/A1</p>
                </div>

            </div>

            <!-- 灭菌分类 -->
            <h2 class="category-title">灭菌</h2>
            <div class="form-list">

                <div class="form-card" onclick="navigateToForm('环氧乙烷气罐使用登记表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">环氧乙烷气罐使用登记表</h3>
                    <p class="form-desc">XCWI037-F02A</p>
                </div>

                <div class="form-card" onclick="navigateToForm('低温环氧乙烷灭菌器检查及清洁记录表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">低温环氧乙烷灭菌器检查及清洁记录表</h3>
                    <p class="form-desc">XCWI037-F01/A1</p>
                </div>
                <div class="form-card" onclick="navigateToForm('灭菌区设备清洁保养记录')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">灭菌区设备清洁保养记录</h3>
                    <p class="form-desc">XCWI035-F02B</p>
                </div>
                <div class="form-card" onclick="navigateToForm('不合格产品包登记表')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">不合格产品包登记表</h3>
                    <p class="form-desc">XCWI014-F05A</p>
                </div>
                <div class="form-card" onclick="navigateToForm('无菌发放区物表清洁及消毒记录')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">无菌发放区物表清洁及消毒记录</h3>
                    <p class="form-desc">AHWI028-F04A1</p>
                </div>
                <div class="form-card" onclick="navigateToForm('压力蒸汽灭菌器运行前检查记录')">
                    <div class="form-icon">⚠️</div>
                    <h3 class="form-name">压力蒸汽灭菌器运行前检查记录</h3>
                    <p class="form-desc">XCWI014-F01A/F10A</p>
                </div>

            </div>

        </div>

        <!-- 质量分类 -->
        <div class="tab-content active" id="quality-content">
            <!-- 每日分类 -->
            <h2 class="category-title">每日填写</h2>
            <div class="form-list">
                <div class="form-card" onclick="navigateToForm('手持式ATP测试记录')">
                    <div class="form-icon">🧼</div>
                    <h3 class="form-name">手持式ATP测试记录</h3>
                    <p class="form-desc">AHWI021-F04B</p>
                </div>
                <div class="form-card" onclick="navigateToForm('去污区预清洗质量抽查记录表')">
                    <div class="form-icon">🫗</div>
                    <h3 class="form-name">去污区预清洗质量抽查记录表</h3>
                    <p class="form-desc">AHWI074-F01A0</p>
                </div>
                <div class="form-card" onclick="navigateToForm('生产物料检验记录表')">
                    <div class="form-icon">🔬</div>
                    <h3 class="form-name">生产物料检验记录表</h3>
                    <p class="form-desc">AHWI023-F01A</p>
                </div>
                <div class="form-card" onclick="navigateToForm('外来器械清洗-一检')">
                    <div class="form-icon">🚑</div>
                    <h3 class="form-name">外来器械清洗-一检</h3>
                    <p class="form-desc">AHWI021-F27-2 A1</p>
                </div>
                <div class="form-card" onclick="navigateToForm('外来器械清洗-术后、预清洗')">
                    <div class="form-icon">🚑</div>
                    <h3 class="form-name">外来器械清洗-术后、预清洗</h3>
                    <p class="form-desc">AHWI021-F27-1 A1</p>
                </div>
                <div class="form-card" onclick="navigateToForm('灭菌装载合格率抽查记录表')">
                    <div class="form-icon">🚑</div>
                    <h3 class="form-name">灭菌装载合格率抽查记录表</h3>
                    <p class="form-desc">AHWI021-F28A</p>
                </div>
                <div class="form-card" onclick="navigateToForm('布类抽检表')">
                    <div class="form-icon">🚑</div>
                    <h3 class="form-name">布类抽检表</h3>
                    <p class="form-desc">布类抽检表</p>
                </div>
            </div>

            <h2 class="category-title">每周填写</h2>
            <div class="form-list">
                <div class="form-card" onclick="navigateToForm('纯化水质量检测记录（申博）')">
                    <div class="form-icon">🚑</div>
                    <h3 class="form-name">纯化水质量检测记录（申博）</h3>
                    <p class="form-desc">AHWI021-F02-02 A0</p>
                </div>
                <div class="form-card" onclick="navigateToForm('纯化水质量检测记录（申梁）')">
                    <div class="form-icon">🚑</div>
                    <h3 class="form-name">纯化水质量检测记录（申梁）</h3>
                    <p class="form-desc">AHWI021-F02-02 A0</p>
                </div>
                <div class="form-card" onclick="navigateToForm('纯化水质量检测记录（申虹）')">
                    <div class="form-icon">🚑</div>
                    <h3 class="form-name">纯化水质量检测记录（申虹）</h3>
                    <p class="form-desc">AHWI021-F02-02 A0</p>
                </div>
            </div>
            <h2 class="category-title">每月填写</h2>
            <div class="form-list">
                <div class="form-card" onclick="navigateToForm('紫外线灯测试卡记录')">
                    <div class="form-icon">🧼</div>
                    <h3 class="form-name">紫外线灯测试卡记录</h3>
                    <p class="form-desc">AHWI028－F15A</p>
                </div>
                <div class="form-card" onclick="navigateToForm('蛋白残留测试记录')">
                    <div class="form-icon">🧼</div>
                    <h3 class="form-name">蛋白残留测试记录</h3>
                    <p class="form-desc">AHWI021-F05A1</p>
                </div>
                <div class="form-card" onclick="navigateToForm('工作区照度检测记录')">
                    <div class="form-icon">🧼</div>
                    <h3 class="form-name">工作区照度检测记录</h3>
                    <p class="form-desc">AHWI021-F08 A1</p>
                </div>

            </div>

        </div>

    </div>
</div>
<script>
    $load(function () {
        // 所有表单数据
        const allForms = [
            // 运营 - 多区域通用
            {
                name: '医用封口机检查测试记录表',
                category: '多区域通用',
                desc: 'XCWI034-F01A',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=135'
            },


            // 运营 - 去污
            {
                name: '清洗消毒器',
                category: '去污',
                desc: '清洗消毒器使用记录表',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=54'
            },
            {
                name: '去污区物表清洁及消毒记录',
                category: '去污',
                desc: '去污区物体表面清洁消毒记录表',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=57'
            },
            {
                name: '（酒精）危化品使用登记表',
                category: '去污',
                desc: '（酒精）危化品使用登记表',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=60'
            },
            {
                name: '超声清洗机检查及配液配置记录表',
                category: '去污',
                desc: 'XCWI030-F01A1',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=95'
            },

            {
                name: '敷料区物表清洁及消毒记录',
                category: '去污',
                desc: 'AHWI028-F05/A1',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=104'
            },
            {
                name: '煮沸消毒器使用前检查及日常清洁保养表',
                category: '去污',
                desc: 'AHWI075-F01/A0',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=108'
            },
            {
                name: '紫外线消毒记录',
                category: '去污',
                desc: 'AHWI028-F14A1',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=105'
            },
            {
                name: '酸化水pH、有效氯检测记录表',
                category: '去污',
                desc: 'XCWI040-F01A2-02',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=120'
            },
            {
                name: '去污区辅助设备清洁及点检记录表',
                category: '去污',
                desc: 'XCWI030-F03A',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=123'
            },
            {
                name: '清洗层架检查记录表',
                category: '去污',
                desc: 'XCWI104-F01A',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=126'
            },
            {
                name: '内镜工作站使用后清洁消毒登记表',
                category: '去污',
                desc: 'XCWI084-F01A0',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=129'
            },
            {
                name: '水池清洗剂、除锈剂配置记录表',
                category: '去污',
                desc: 'XCWI094-F01A',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=132'
            },


            // 运营 - 检查包装
            {
                name: '绝缘检测仪每日点检记录表',
                category: '检查包装',
                desc: 'XCWI041-F01A',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=111'
            },
            {
                name: '检查包装区设备清洁保养记录',
                category: '检查包装',
                desc: 'XCWI009-F05B',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=114'
            },
            {
                name: '检查包装区物表清洁及消毒记录',
                category: '检查包装',
                desc: 'AHWI028-F03A1',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=117'
            },

            // 运营 - 灭菌发放
            {
                name: '环氧乙烷气罐使用登记表',
                category: '灭菌',
                desc: 'AHWI028-F03A1',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=138'
            },
            {
                name: '低温环氧乙烷灭菌器检查及清洁记录表',
                category: '灭菌',
                desc: 'XCWI037-F01/A1',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=141'
            },
            {
                name: '灭菌区设备清洁保养记录',
                category: '灭菌',
                desc: 'XCWI035-F02B/FO3A',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=144'
            },
            {
                name: '不合格产品包登记表',
                category: '灭菌',
                desc: 'XCWI014-F05A',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=147'
            },
            {
                name: '无菌发放区物表清洁及消毒记录',
                category: '灭菌',
                desc: 'AHWI028-F04A1',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=150'
            },
            {
                name: '压力蒸汽灭菌器运行前检查记录',
                category: '灭菌',
                desc: 'XCWI014-F01A/F10A',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=153'
            },
            // 质量 - 每日填写
            {
                name: '手持式ATP测试记录',
                category: '每日填写',
                desc: 'AHWI021-F04B',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=67'
            },
            {
                name: '去污区预清洗质量抽查记录表',
                category: '每日填写',
                desc: 'AHWI074-F01A0',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=72'
            },
            {
                name: '生产物料检验记录表',
                category: '每日填写',
                desc: 'AHWI023-F01A',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=75'
            },
            {
                name: '外来器械清洗-一检',
                category: '每日填写',
                desc: 'AHWI021-F27-2 A1',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=79'
            },
            {
                name: '外来器械清洗-术后、预清洗',
                category: '每日填写',
                desc: 'AHWI021-F27-1 A1',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=82'
            },
            {
                name: '灭菌装载合格率抽查记录表',
                category: '每日填写',
                desc: 'AHWI021-F28A',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=86'
            },
            {
                name: '布类抽检表',
                category: '每日填写',
                desc: '布类抽检表',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=92'
            },
            // 质量 - 每周填写
            {
                name: '纯化水质量检测记录（申博）',
                category: '每周填写',
                desc: 'AHWI021-F02-02 A0',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=174'
            },
            {
                name: '纯化水质量检测记录（申梁）',
                category: '每周填写',
                desc: 'AHWI021-F02-02 A0',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=177'
            },
            {
                name: '纯化水质量检测记录（申虹）',
                category: '每周填写',
                desc: 'AHWI021-F02-02 A0',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=186'
            },


            // 质量 - 每月填写
            {
                name: '紫外线灯测试卡记录',
                category: '每月填写',
                desc: 'AHWI028－F15A',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=165'
            },

            {
                name: '蛋白残留测试记录',
                category: '每月填写',
                desc: 'AHWI021-F05A1',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=168'
            },
            {
                name: '工作区照度检测记录',
                category: '每月填写',
                desc: 'AHWI021-F05A1',
                url: '/mobilemode/appHomepageView.jsp?appHomepageId=171'
            }


        ];

        // DOM元素
        const searchInput = document.getElementById('searchInput');
        const searchMatched = document.getElementById('searchMatched');
        const matchedForms = document.getElementById('matchedForms');
        const originalForms = document.getElementById('originalForms');
        const tabItems = document.querySelectorAll('.tab-item');
        const tabContents = document.querySelectorAll('.tab-content');

        // 初始化函数
        function initializeApp() {
            console.log('wax');
            // 初始化tab状态
            switchTab('operation');

            // 加载时渲染已有历史
            renderSearchHistory();

            // 绑定tab点击事件
            bindTabEvents();

            // 绑定搜索事件
            bindSearchEvents();

            // 绑定卡片点击事件
            bindCardEvents();

            // 绑定删除按钮点击事件
            bindClearButtonEvents();
        }

        // Tab切换功能
        function switchTab(tabId) {
            console.log('Switching to tab:', tabId); // 调试日志
            console.log('searchInput.value BEFORE tab switch operations:', searchInput ? searchInput.value : 'N/A');

            // 移除所有active类
            tabItems.forEach(item => {
                item.classList.remove('active');
            });

            tabContents.forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
            });

            // 添加当前active类
            const currentTab = document.querySelector(`[data-tab="${tabId}"]`);
            if (currentTab) {
                currentTab.classList.add('active');
            }

            // 显示对应的内容
            const currentContent = document.getElementById(`${tabId}-content`);
            if (currentContent) {
                currentContent.classList.add('active');
                currentContent.style.display = 'block';
            }

            console.log('searchInput.value AFTER setting active tab content:', searchInput ? searchInput.value : 'N/A');

            // 切换tab时，根据当前搜索关键词重新执行搜索，不清空搜索框
            if (searchInput) {
                performSearch(searchInput.value.trim());
            }

            // 根据当前tab重置全局显示状态
            if (searchMatched) {
                searchMatched.style.display = 'none';
            }
            if (originalForms) {
                originalForms.style.display = 'block';
            }
            console.log('searchInput.value AFTER performSearch and display reset:', searchInput ? searchInput.value : 'N/A');
        }

        // 绑定Tab事件
        function bindTabEvents() {
            tabItems.forEach(tab => {
                tab.addEventListener('click', function (e) {
                    console.log('Tab (click) triggered:', this.getAttribute('data-tab'));
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId);
                });
            });
        }

        // 搜索功能
        function performSearch(keyword) {

            console.log('performSearch called with keyword:', keyword); // Debug log
            console.log('searchInput.value INSIDE performSearch (start):', searchInput ? searchInput.value : 'N/A');

            const activeTab = document.querySelector('.tab-item.active');
            if (!activeTab) return;

            const activeTabId = activeTab.getAttribute('data-tab');

            // 质量tab的处理逻辑
            if (activeTabId === 'quality') {
                console.log('performSearch: Active tab is quality.');
                if (!keyword) {
                    console.log('performSearch: Keyword is empty, calling resetSearch().');
                    resetSearch(); // 没有关键词时重置所有显示
                    console.log('searchInput.value INSIDE performSearch (operation branch, after resetSearch):', searchInput ? searchInput.value : 'N/A');
                    return;
                }

                let hasAnyVisible = false;
                document.querySelectorAll('#quality-content .form-list').forEach(list => { // 明确只处理质量tab的内容
                    const cards = list.querySelectorAll('.form-card');
                    let hasVisible = false;

                    cards.forEach(card => {
                        const name = card.querySelector('.form-name') ? card.querySelector('.form-name').textContent : '';
                        const desc = card.querySelector('.form-desc') ? card.querySelector('.form-desc').textContent : '';

                        if (name.toLowerCase().includes(keyword) || desc.toLowerCase().includes(keyword)) {
                            card.style.display = '';
                            hasVisible = true;
                            hasAnyVisible = true;
                        } else {
                            card.style.display = 'none';
                        }
                    });

                    const noData = list.querySelector('.no-data');
                    if (noData) {
                        noData.style.display = hasVisible ? 'none' : '';
                    }

                    const prevTitle = list.previousElementSibling;
                    if (hasVisible) {
                        list.style.display = '';
                        if (prevTitle && prevTitle.classList.contains('category-title')) {
                            prevTitle.style.display = '';
                        }
                    } else {
                        list.style.display = 'none';
                        if (prevTitle && prevTitle.classList.contains('category-title')) {
                            prevTitle.style.display = 'none'; // 确保当列表无可见卡片时，标题也隐藏
                        }
                    }
                });

                if (!hasAnyVisible) {
                    searchMatched.style.display = 'block';
                    originalForms.style.display = 'none';
                    matchedForms.innerHTML = '<div class="no-data">没有找到匹配的表单</div>';
                } else {
                    searchMatched.style.display = 'none';
                    originalForms.style.display = 'block';
                }
            }

            // 运营tab的搜索逻辑
            if (activeTabId === 'operation') {
                console.log('performSearch: Active tab is operation.');
                if (!keyword) {
                    console.log('performSearch: Keyword is empty, calling resetSearch().');
                    resetSearch(); // 没有关键词时重置所有显示
                    console.log('searchInput.value INSIDE performSearch (operation branch, after resetSearch):', searchInput ? searchInput.value : 'N/A');
                    return;
                }

                let hasAnyVisible = false;
                document.querySelectorAll('#operation-content .form-list').forEach(list => { // 明确只处理运营tab的内容
                    const cards = list.querySelectorAll('.form-card');
                    let hasVisible = false;

                    cards.forEach(card => {
                        const name = card.querySelector('.form-name') ? card.querySelector('.form-name').textContent : '';
                        const desc = card.querySelector('.form-desc') ? card.querySelector('.form-desc').textContent : '';

                        if (name.toLowerCase().includes(keyword) || desc.toLowerCase().includes(keyword)) {
                            card.style.display = '';
                            hasVisible = true;
                            hasAnyVisible = true;
                        } else {
                            card.style.display = 'none';
                        }
                    });

                    const noData = list.querySelector('.no-data');
                    if (noData) {
                        noData.style.display = hasVisible ? 'none' : '';
                    }

                    const prevTitle = list.previousElementSibling;
                    if (hasVisible) {
                        list.style.display = '';
                        if (prevTitle && prevTitle.classList.contains('category-title')) {
                            prevTitle.style.display = '';
                        }
                    } else {
                        list.style.display = 'none';
                        if (prevTitle && prevTitle.classList.contains('category-title')) {
                            prevTitle.style.display = 'none'; // 确保当列表无可见卡片时，标题也隐藏
                        }
                    }
                });

                if (!hasAnyVisible) {
                    searchMatched.style.display = 'block';
                    originalForms.style.display = 'none';
                    matchedForms.innerHTML = '<div class="no-data">没有找到匹配的表单</div>';
                } else {
                    searchMatched.style.display = 'none';
                    originalForms.style.display = 'block';
                }
            }
        }

        // 重置搜索
        function resetSearch() {
            console.log('resetSearch called.');
            console.log('searchInput.value INSIDE resetSearch (start):', searchInput ? searchInput.value : 'N/A');
            // 确保只重置运营tab下的内容
            document.querySelectorAll('#operation-content .form-card').forEach(card => {
                card.style.display = '';
            });

            document.querySelectorAll('#operation-content .form-list').forEach(list => {
                list.style.display = '';
                const prevTitle = list.previousElementSibling;
                if (prevTitle && prevTitle.classList.contains('category-title')) {
                    prevTitle.style.display = '';
                }
                const noData = list.querySelector('.no-data');
                if (noData) noData.style.display = ''; // 恢复no-data显示
            });

            searchMatched.style.display = 'none';
            originalForms.style.display = 'block';
            console.log('searchInput.value INSIDE resetSearch (end):', searchInput ? searchInput.value : 'N/A');
        }

        // 绑定搜索事件
        function bindSearchEvents() {
            if (!searchInput) return;

            // 使用input事件
            searchInput.addEventListener('input', function (e) {
                const keyword = this.value.trim().toLowerCase();
                performSearch(keyword);

                //  if (e.isTrusted && keyword) {
                //     debouncedSaveHistory(keyword);
                //  }
            });

            // 使用change事件
            searchInput.addEventListener('change', function (e) {
                const keyword = this.value.trim().toLowerCase();
                performSearch(keyword);

                //  if (e.isTrusted && keyword) {
                //   debouncedSaveHistory(keyword);
                // }
            });
        }

        // 绑定卡片点击事件
        function bindCardEvents() {
            document.querySelectorAll('.form-card').forEach(card => {
                card.addEventListener('click', function (e) {
                    console.log('Card (click) triggered:', this.querySelector('.form-name').textContent);
                    const formName = this.querySelector('.form-name').textContent;
                    navigateToForm(formName);

                    saveSearchHistory(formName);
                });
            });
        }

        // 绑定删除按钮点击事件
        function bindClearButtonEvents() {
            const clearAllBtn = document.querySelector('.clear-all-btn');
            if (clearAllBtn) {
                clearAllBtn.addEventListener('click', () => {
                    localStorage.removeItem('searchHistory'); // 清除 localStorage 中所有历史
                    renderSearchHistory(); // 重新渲染（此时显示"暂无历史"）
                });
            }
        }

        // 导航到表单页
        function navigateToForm(formName) {
            const form = allForms.find(f => f.name === formName);
            if (form && form.url) {
                if (typeof $u === 'function') {
                    $u(form.url);
                } else {
                    window.location.href = form.url;
                }
            } else {
                alert('没有找到对应的表单地址');
            }
        }

        // —————— 1. 历史记录核心逻辑 ——————
        // 保存历史（去重、保留最近3条）
        function saveSearchHistory(keyword) {
            let history = localStorage.getItem('searchHistory');
            history = history ? JSON.parse(history) : [];

            // 去重：若关键词已存在，先删除旧记录
            const index = history.indexOf(keyword);
            if (index > -1) {
                history.splice(index, 1);
            }

            // 新记录插入到最前面（保证最新记录在首位）
            history.unshift(keyword);

            // 只保留最近3条
            if (history.length > 3) {
                history = history.slice(0, 3);
            }

            // 存回 localStorage
            localStorage.setItem('searchHistory', JSON.stringify(history));

            // 渲染最新历史到页面
            renderSearchHistory();
        }

        // 渲染历史到页面
        function renderSearchHistory() {
            const historyDiv = document.querySelector('.search-history-section');
            const clearAllBtn = document.querySelector('.clear-all-btn')
            const historyList = historyDiv.querySelector('.tags-list');     // 历史项列表


            // 读取 localStorage 中的历史记录
            let history = localStorage.getItem('searchHistory');
            history = history ? JSON.parse(history) : [];
            console.log('renderSearchHistory', historyDiv, historyList, history)
            // &#128309; 控制「删除全部」按钮显隐：有历史则显示，无则隐藏
            clearAllBtn.style.display = history.length > 0 ? 'inline-block' : 'none';

            // 清空历史项列表（避免重复渲染）
            historyList.innerHTML = '';

            if (history.length === 0) {
                // 无历史时，渲染"暂无历史"提示
                const noHistory = document.createElement('p');
                noHistory.className = 'no-history-tip';
                noHistory.textContent = '暂无历史搜索';
                historyList.appendChild(noHistory);
                return;
            }

            // &#128309; 有历史时，渲染每个历史项到列表
            history.forEach((keyword) => {
                const li = document.createElement('li');
                li.textContent = keyword;
                // 点击历史项：回填到输入框 + 触发搜索（不重复保存历史）
                li.addEventListener('click', () => {
                    searchInput.value = keyword;
                    performSearch(keyword.trim().toLowerCase());
                });
                historyList.appendChild(li);
            });
        }

        // 初始化应用
        initializeApp();

        document.querySelectorAll('.tab-content').forEach(content => {
            if (content.classList.contains('active')) {
                content.style.display = 'block';
            } else {
                content.style.display = 'none';
            }
        });
    });
</script>
</body>

</html>