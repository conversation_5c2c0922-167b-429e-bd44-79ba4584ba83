ecodeSDK.rewriteRouteQueue.push({
    fn: (params) => {
        const {Com, Route, nextState} = params;
        const cpParams = {
            path: 'main/cs/app', //路由地址
            appId: '${appId}',
            name: 'page', //具体页面应用id
            node: 'app', //渲染的路由节点，这里渲染的是app这个节点
            Route,
            nextState
        }
        if (ecodeSDK.checkPath(cpParams)) { //判断地址是否是要注入的地址
            const acParams = {
                appId: cpParams.appId,
                name: cpParams.name, //模块名称
                props: params, //参数
                isPage: true, //是否是路由页面
                noCss: false //是否禁止单独加载css，通常为了减少css数量，css默认前置加载
            }
            //异步加载模块${appId}下的子模块pageSimple
            return ecodeSDK.getAsyncCom(acParams);
        }
        return null; //这里一定要返回空，不然会干扰到其它新页面
    },
    order: 10,
    desc: 'Demo简单页面'
});