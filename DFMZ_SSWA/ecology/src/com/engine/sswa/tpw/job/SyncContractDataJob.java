package com.engine.sswa.tpw.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.doc.detail.service.DocAccService;
import com.api.doc.detail.service.DocSaveService;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.util.HttpUtil;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sswa.tpw.commom.bean.ContractBean;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @FileName TesT
 * @Description 合同数据 SyncContractDataJob
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/26
 */
@Slf4j
@Getter
@Setter
public class SyncContractDataJob extends BaseCronJob {
    BaseBean bb = new BaseBean();
    //系统标识（由OA分配）
    private String systemid;
    //密码
    private String password;
    //东方明珠地址
    private String OPGaddress;
    //接口单次请求数量
    private String pageSize;
    //合同查询接口
    private String contractUrl;
    //合同附件获取接口
    private String contractAnnexUrl;
    //目录id
    private String catId;
    //ap-id
    private String appid;
    //loginid
    private String loginid;

    private ArrayList<String> htbhs;

    /**
     * 获取当前日期时间。 YYYY-MM-DD HH:MM:SS
     *
     * @return 当前日期时间
     */
    public static String getCurDateTime() {
        Date newdate = new Date();
        long datetime = newdate.getTime();
        Timestamp timestamp = new Timestamp(datetime);
        return (timestamp.toString()).substring(0, 19);
    }

    /**
     * 获取时间戳   格式如：19990101235959
     *
     * @return
     */
    public static String getTimestamp() {
        return getCurDateTime().replace("-", "").replace(":", "").replace(" ", "");
    }

    public static String getMD5Str(String plainText) {
        //定义一个字节数组
        byte[] secretBytes = null;
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            //对字符串进行加密
            md.update(plainText.getBytes());
            //获得加密后的数据
            secretBytes = md.digest();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("没有md5这个算法！");
//            throw new RuntimeException(SystemEnv.getHtmlLabelName(517545,userLanguage));
        }
        //将加密后的数据转换为16进制数字
        String md5code = new BigInteger(1, secretBytes).toString(16);
        // 如果生成数字未满32位，需要前面补0
        // 不能把变量放到循环条件，值改变之后会导致条件变化。如果生成30位 只能生成31位md5
        int tempIndex = 32 - md5code.length();
        for (int i = 0; i < tempIndex; i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }
    public static String getParamValueFromUrl(String url, String paramName) {
        if (url == null || paramName == null) {
            return null;
        }
        // 使用正则表达式匹配参数
        String pattern = "(?<=[?&])" + paramName + "=([^&]*)";
        Matcher matcher = Pattern.compile(pattern).matcher(url);

        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }


    @Override
    public void execute() {
        bb.writeLog("SyncContractDataJob start");
        try {
            if (StringUtils.isNotBlank(systemid) && StringUtils.isNotBlank(password) && StringUtils.isNotBlank(OPGaddress) && StringUtils.isNotBlank(contractUrl)
                    && StringUtils.isNotBlank(contractAnnexUrl) && StringUtils.isNotBlank(pageSize) && StringUtils.isNotBlank(catId)
                    && StringUtils.isNotBlank(loginid) && StringUtils.isNotBlank(appid)) {
                resetParam();
                init();
                //获取股份公司合同管理系统的全量合同数据
                List<ContractBean> contractBeans = getContractData();
                if (!contractBeans.isEmpty()) {
                    //将增量数据新增写入尚世五岸项目管理系统
                    disposeContractDatas(contractBeans);
                }

                bb.writeLog("contractBeans:" + JSON.toJSONString(contractBeans));
                if (!contractBeans.isEmpty()) {
                    int moduleId = ModuleDataUtil.getModuleIdByName(ContractBean.TABLE_NAME);
                    ModuleDataUtil.insertObjList(contractBeans, ContractBean.TABLE_NAME, moduleId, 1);
                }
                // 获取股份公司合同管理系统的全量合同附件数据
                HashMap<String, JSONArray> contractAnnexDatas = getContractAnnexData();

                if (!contractAnnexDatas.isEmpty()) {
                    // 处理合同附件数据
                    disposeContractAnnexDatas(contractAnnexDatas);
                    bb.writeLog("contractAnnexDatas:" + JSON.toJSONString(contractAnnexDatas));
                    // 系统中创建附件，通过附件生成文档 ID
                    // 根据合同编号将文档 ID 更新到具体的建模记录中
                    updateContractDocuments(contractAnnexDatas);
                }
            } else {
                bb.writeLog("SyncContractDataJob 定时任务请求参数未填写完整,请检查定时任务参数配置");
            }
            bb.writeLog("SyncContractDataJob end");
        } catch (Exception e) {
            bb.writeLog("SyncContractDataJob Exception:" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private void disposeContractAnnexDatas(HashMap<String, JSONArray> contractAnnexDatas) {
        // 使用迭代器遍历 HashMap 并移除符合条件的条目
        contractAnnexDatas.keySet().removeIf(htbhs::contains);
    }

    private void disposeContractDatas(List<ContractBean> contractBeans) {
        contractBeans.removeIf(contractBean -> htbhs.contains(contractBean.getHtbh()));
    }

    private void init() {
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        recordSet.executeQuery("select htbh from uf_httz");
        while (recordSet.next()) {
            htbhs.add(recordSet.getString("htbh"));
        }
    }

    private void resetParam() {
        htbhs = new ArrayList<>();
    }

    /**
     * 更新合同附件对应的文档 ID
     *
     * @param contractAnnexDatas 合同附件数据
     */
    private void updateContractDocuments(HashMap<String, JSONArray> contractAnnexDatas) throws Exception {
        long lastTokenTime = 0;
        long tokenRefreshInterval = 10 * 60 * 1000; // 20分钟（毫秒）
        String token = getToken();
        lastTokenTime = System.currentTimeMillis();
        for (Map.Entry<String, JSONArray> entry : contractAnnexDatas.entrySet()) {

            long currentTime = System.currentTimeMillis();
            if (currentTime - lastTokenTime > tokenRefreshInterval) {
                token = getToken();
                lastTokenTime = currentTime;
            }
            if (StringUtils.isNotBlank(token)) {
                String contractNumber = entry.getKey(); // 合同编号
                JSONArray fjmcArray = entry.getValue(); // 对应附件数据
                ArrayList<String> documentIds = new ArrayList<>();
                try {
                    for (int i = 0; i < fjmcArray.size(); i++) {
                        JSONObject jsonObject = fjmcArray.getJSONObject(i);
                        String content = Util.null2String(jsonObject.get("content"));
                        String name = Util.null2String(jsonObject.get("name"));
                        if (StringUtils.isNotBlank(content)) {

                            // 以?为分隔符拆分URL
                            String[] parts = content.split("\\?", 2);  // 使用2表示最多分成两部分

                            if (parts.length > 1) {
                                String basePath = parts[0];  // "/weaver/weaver.file.FileDownload"
                                String queryString = parts[1]; // 参数部分
                                String downloadPath = basePath + "?ssotoken=" + token + "&" + queryString;
                                String url = OPGaddress + downloadPath;
                                log.info("name : " + name + " url : " + url);
                                //通过url生成文档id
                                DocAccService docAccService = new DocAccService();
                                DocSaveService docSaveService = new DocSaveService();
                                //下载网络url文件，生成imagefileid
                                int imagefileid = docAccService.getFileByUrl(url, name);
                                User userSqr = new User(1);
                                if (imagefileid > 0) {
                                    //附件生成文档
                                    int docId = docSaveService.accForDoc(Integer.parseInt(catId), imagefileid, userSqr);
                                    documentIds.add(String.valueOf(docId));
                                }
                            }
                        }
                    }
                    updateModelRecordWithDocumentId(contractNumber, String.join(",", documentIds));
                } catch (Exception e) {
                    // 捕获并记录异常，避免影响其他记录的处理
                    bb.writeLog("处理合同附件失败，合同编号: " + contractNumber);
                }
            }
        }
    }

    /**
     * 示例：将文档 ID 更新到具体的建模记录中
     *
     * @param contractNumber 合同编号
     * @param documentIds    文档 ID
     */
    private void updateModelRecordWithDocumentId(String contractNumber, String documentIds) {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            String sql = "update uf_httz set htfj = '" + documentIds + "' where htbh = '" + contractNumber + "'";
            recordSet.executeUpdate(sql);
        } catch (Exception e) {
            bb.writeLog("将文档 ID 更新到具体的建模记录中异常" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    /**
     * restful接口调用案例
     * 以getModeDataPageList为例
     */
    public List<ContractBean> getContractData() {
        List<ContractBean> contractBeans = new ArrayList<>();
        CloseableHttpClient httpClient = HttpClients.createDefault();
        // 定义 pageNo 初始值
        int pageNo = 1;

        while (true) {
            CloseableHttpResponse response = null;
            try {
                HttpPost httpPost = new HttpPost(contractUrl);

                // 获取时间戳
                String currentTimeTamp = getTimestamp();

                // 设置 header 参数
                HashMap<String, String> header = new HashMap<>();
                header.put("systemid", systemid);
                header.put("currentDateTime", currentTimeTamp);
                String md5Source = systemid + password + currentTimeTamp;
                String md5OfStr = getMD5Str(md5Source).toLowerCase();
                header.put("Md5", md5OfStr);

                // 设置请求体参数
                JSONObject paramDatajson = new JSONObject();
                paramDatajson.put("header", header);

                JSONObject pageInfo = new JSONObject();
                pageInfo.put("pageNo", pageNo);
                pageInfo.put("pageSize", pageSize);
                paramDatajson.put("pageInfo", pageInfo);

                JSONObject mainTable = new JSONObject();
                mainTable.put("id", "1");
                paramDatajson.put("mainTable", mainTable);

                JSONObject operationinfo = new JSONObject();
                operationinfo.put("operator", "1");
                paramDatajson.put("operationinfo", operationinfo);

                HashMap<String, Object> params = new HashMap<>();
                params.put("datajson", paramDatajson);

                List<BasicNameValuePair> nvps = new ArrayList<>();
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    nvps.add(new BasicNameValuePair(entry.getKey(), JSONObject.toJSONString(entry.getValue())));
                }

                httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
                httpPost.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"));

                response = httpClient.execute(httpPost);
                bb.writeLog("处理合同数据接口，" + pageNo + "response: " + JSONObject.toJSONString(response));
                if (response != null && response.getEntity() != null) {
                    String resultString = EntityUtils.toString(response.getEntity());
                    JSONObject responseJson = JSONObject.parseObject(resultString);
                    String resultStr = Util.null2String(responseJson.get("result"));
                    JSONArray result = JSONArray.parseArray(resultStr);

                    for (int i = 0; i < result.size(); i++) {
                        JSONObject jsonObject = result.getJSONObject(i);
                        JSONObject mainObj = jsonObject.getJSONObject("mainTable");
                        ContractBean contractBean = new ContractBean();
                        contractBean.setHtbh(Util.null2String(mainObj.get("htbh")));
                        contractBean.setHtmc(Util.null2String(mainObj.get("htmc")));
                        contractBean.setHtje(Float.parseFloat("".equals(Util.null2String(mainObj.get("htze"))) ? "0" : Util.null2String(mainObj.get("htze"))));
                        contractBean.setJbr(Util.null2String(mainObj.get("jbr")));
                        contractBean.setLylc(Util.null2String(mainObj.get("lylc")));
                        contractBeans.add(contractBean);
                    }

                    // 如果返回结果数量小于 1000，则结束循环
                    if (result.size() < Integer.parseInt(pageSize)) {
                        break;
                    }
                    // 否则增加 pageNo，继续请求
                    pageNo++;
                }
            } catch (Exception e) {
                e.printStackTrace();
                break;
            } finally {
                try {
                    if (response != null) {
                        response.close();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return contractBeans;
    }

    public HashMap<String, JSONArray> getContractAnnexData() {
        HashMap<String, JSONArray> map = new HashMap<>();
        CloseableHttpClient httpClient = HttpClients.createDefault();
        // 定义 pageNo 初始值
        int pageNo = 1;

        while (true) {
            CloseableHttpResponse response = null;
            try {
                HttpPost httpPost = new HttpPost(contractAnnexUrl);

                // 获取时间戳
                String currentTimeTamp = getTimestamp();

                // 设置 header 参数
                HashMap<String, String> header = new HashMap<>();
                header.put("systemid", systemid);
                header.put("currentDateTime", currentTimeTamp);
                String md5Source = systemid + password + currentTimeTamp;
                String md5OfStr = getMD5Str(md5Source).toLowerCase();
                header.put("Md5", md5OfStr);

                // 设置请求体参数
                JSONObject paramDatajson = new JSONObject();
                paramDatajson.put("header", header);

                JSONObject pageInfo = new JSONObject();
                pageInfo.put("pageNo", pageNo);
                pageInfo.put("pageSize", pageSize);
                paramDatajson.put("pageInfo", pageInfo);

                JSONObject mainTable = new JSONObject();
                mainTable.put("id", "1");
                paramDatajson.put("mainTable", mainTable);

                JSONObject operationinfo = new JSONObject();
                operationinfo.put("operator", "1");
                paramDatajson.put("operationinfo", operationinfo);

                HashMap<String, Object> params = new HashMap<>();
                params.put("datajson", paramDatajson);

                List<BasicNameValuePair> nvps = new ArrayList<>();
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    nvps.add(new BasicNameValuePair(entry.getKey(), JSONObject.toJSONString(entry.getValue())));
                }

                httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
                httpPost.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"));

                response = httpClient.execute(httpPost);
                bb.writeLog("处理合同附件数据接口，" + pageNo + "response: " + JSONObject.toJSONString(response));

                if (response != null && response.getEntity() != null) {
                    String resultString = EntityUtils.toString(response.getEntity());
                    JSONObject responseJson = JSONObject.parseObject(resultString);
                    String resultStr = Util.null2String(responseJson.get("result"));
                    JSONArray result = JSONArray.parseArray(resultStr);
                    for (int i = 0; i < result.size(); i++) {
                        JSONObject jsonObject = result.getJSONObject(i);
                        JSONObject mainObj = JSONObject.parseObject(Util.null2String(jsonObject.get("mainTable")));
                        String htbh = Util.null2String(mainObj.get("htbh"));
                        String fjmc = Util.null2String(mainObj.get("fjmc"));
                        map.put(htbh, JSONArray.parseArray(fjmc));
                    }
                    // 如果返回结果数量小于 1000，则结束循环
                    if (result.size() < Integer.parseInt(pageSize)) {
                        break;
                    }
                    // 否则增加 pageNo，继续请求
                    pageNo++;
                }
            } catch (Exception e) {
                e.printStackTrace();
                break;
            } finally {
                try {
                    if (response != null) {
                        response.close();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return map;
    }

    public String getToken() throws Exception {
        String token = "";
        try {
            Map<String, Object> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("appid", appid);
            stringStringHashMap.put("loginid", loginid);
            String restResult = HttpUtil.postWeaverData(OPGaddress + "/ssologin/getToken", "", "", "", stringStringHashMap);
            bb.writeLog("restResult:" + restResult);
            token = restResult;
        } catch (Exception e) {
            bb.writeLog("getToken Exception:" + SDUtil.getExceptionDetail(e));
        }
        return token;
    }
}


